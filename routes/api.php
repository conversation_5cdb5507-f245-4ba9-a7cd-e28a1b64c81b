<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return response()->json([
        'message' => 'Если вы хотите использовать gunpost.ru по api, то свяжитесь администратором',
    ]);
});

Route::post('/telegram/webhook', [\App\Http\Controllers\TelegramWebhookController::class, 'handle']);

Route::get('/payment', [\App\Http\Controllers\RobokassaController::class, 'makeServerPayment']);
Route::post('/payment/webhook', [\App\Http\Controllers\RobokassaController::class, 'webhook']);

Route::post('/payment/yoo/webhook', [\App\Http\Controllers\YookassaController::class, 'webHookHandle']);

// Защищенные статическим ключом маршруты для импорта
Route::middleware([\App\Http\Middleware\StaticKeyAuth::class])->group(function () {
    Route::get('/import', [\App\Http\Controllers\ImportController::class, 'store']);
});

Route::get('/search', [\App\Http\Controllers\SearchController::class, 'search']);
Route::get('/search-debug', [\App\Http\Controllers\SearchController::class, 'searchDebug']);

Route::post('/login', [\App\Http\Controllers\AuthController::class, 'login']);
Route::post('/send-code', [\App\Http\Controllers\AuthController::class, 'sendCode']);
Route::post('/send-telegram-code', [\App\Http\Controllers\AuthController::class, 'sendTelegramCode']);
Route::post('/verify-code', [\App\Http\Controllers\AuthController::class, 'verifyCode']);

Route::get('/posts', [\App\Http\Controllers\PostController::class, 'index']);
Route::get('/posts/{post:slug}', [\App\Http\Controllers\PostController::class, 'show']);
Route::post('/posts/{post:slug}/phone', [\App\Http\Controllers\PostController::class, 'getPhone']);
Route::post('/posts/{post:slug}/report', [\App\Http\Controllers\PostController::class, 'report']);

Route::get('/events', [\App\Http\Controllers\EventsController::class, 'index']);
Route::get('/events/{event:slug}', [\App\Http\Controllers\EventsController::class, 'show']);

Route::get('/users/{user}', [\App\Http\Controllers\ProfileController::class, 'public']);

Route::get('/models', [\App\Http\Controllers\PostCreateController::class, 'getModels']);
Route::get('/calibers', [\App\Http\Controllers\PostCreateController::class, 'getCalibers']);
Route::get('/cities', [\App\Http\Controllers\PostCreateController::class, 'getCities']);

Route::get('/map/ollr', [\App\Http\Controllers\GeoItemsController::class, 'getOLLR']);

Route::post('/post', [\App\Http\Controllers\PostCreateController::class, 'create']);

Route::get('/post/{post:slug}', [\App\Http\Controllers\PostCreateController::class, 'continueCreate']);
Route::post('/post/{post:slug}', [\App\Http\Controllers\PostCreateController::class, 'updateCreate']);
Route::post('/post/{post:slug}/image', [\App\Http\Controllers\PostCreateController::class, 'updateOrCreateImage']);
Route::delete('/post/{post:slug}/image', [\App\Http\Controllers\PostCreateController::class, 'removeImage']);

Route::get('/suggest/address', [\App\Http\Controllers\AddressController::class, 'suggest']);

Route::get('/news', [\App\Http\Controllers\NewsController::class, 'index']);
Route::get('/news/{news:slug}', [\App\Http\Controllers\NewsController::class, 'show']);

Route::middleware(['auth:sanctum'])->group(function () {
    Route::get('user', [\App\Http\Controllers\AuthController::class, 'user'])->name('user');
    Route::post('logout', [\App\Http\Controllers\AuthController::class, 'logout'])->name('user.logout');

    Route::post('user', [\App\Http\Controllers\ProfileController::class, 'update'])->name('user.update');

    Route::post('user/set-city', [\App\Http\Controllers\ProfileController::class, 'setCity'])->name('user.setCity');
    Route::post('user/banner', [\App\Http\Controllers\ProfileController::class, 'acceptBanner']);

    Route::post('user/set-password', [\App\Http\Controllers\ProfileController::class, 'setPassword']);
    Route::post('user/update-password', [\App\Http\Controllers\ProfileController::class, 'updatePassword']);

    Route::get('/user/notifications', [\App\Http\Controllers\NotificationController::class, 'getNotifications']);
    Route::get('/user/notifications/{notification}', [\App\Http\Controllers\NotificationController::class, 'readNotification']);

    Route::get('user/favorites', [\App\Http\Controllers\ProfileController::class, 'favorites']);
    Route::get('/user/posts', [\App\Http\Controllers\PostSettingsController::class, 'posts']);
    Route::post('/user/posts/{post:slug}/approve', [\App\Http\Controllers\PostCreateController::class, 'approveToggle']);
    Route::delete('/user/posts/{post:slug}', [\App\Http\Controllers\PostSettingsController::class, 'delete']);

    Route::get('user/transactions', [\App\Http\Controllers\BalanceController::class, 'show']);
    Route::post('user/deposit', [\App\Http\Controllers\BalanceController::class, 'deposit']);
    Route::post('user/withdraw', [\App\Http\Controllers\BalanceController::class, 'withdraw']);

    Route::post('import', [\App\Http\Controllers\ImportGunsbrokerController::class, 'show']);
    Route::post('import/save', [\App\Http\Controllers\ImportGunsbrokerController::class, 'save']);

    Route::post('/posts/{post:slug}/favorite', [\App\Http\Controllers\PostController::class, 'toggleFavorite']);
    Route::get('/posts/{post:slug}/promotions', [\App\Http\Controllers\PromotionController::class, 'getPromotions']);
    Route::post('/posts/{post:slug}/promotions', [\App\Http\Controllers\PromotionController::class, 'setPromotion']);

    Route::get('/chats', [\App\Http\Controllers\ChatController::class, 'index']);
    Route::get('/chats/support', [\App\Http\Controllers\ChatController::class, 'supportChat']);
    Route::get('/chats/{hashId}', [\App\Http\Controllers\ChatController::class, 'show']);
    Route::post('/chats/{hashId}/message', [\App\Http\Controllers\ChatController::class, 'sendMessage']);
    Route::post('/chats/{hashId}/read', [\App\Http\Controllers\ChatController::class, 'markMessageAsRead']);
    Route::post('/posts/{post:slug}/message', [\App\Http\Controllers\ChatController::class, 'createPostThread']);

    Route::post('/user/telegram/auth', [\App\Http\Controllers\TelegramController::class, 'startMessage']);

    Route::post('/payment/{post:slug}', [\App\Http\Controllers\YookassaController::class, 'makePayment']);

    Route::middleware(\App\Http\Middleware\IsAdminUser::class)->group(function () {
        Route::get('/moderation/posts', [\App\Http\Controllers\ModerationController::class, 'posts']);
        Route::post('/moderation/posts/{post:slug}/moderation', [\App\Http\Controllers\ModerationController::class, 'moderation']);
        Route::post('/moderation/posts/{post:slug}/index', [\App\Http\Controllers\ModerationController::class, 'reIndex']);

        Route::get('/moderation/reports', [\App\Http\Controllers\ModerationController::class, 'reports']);
        Route::get('/moderation/seo', [\App\Http\Controllers\SeoController::class, 'index']);

        Route::post('/moderation/seo/create', [\App\Http\Controllers\SeoController::class, 'create']);
        Route::post('/moderation/seo/{seo}', [\App\Http\Controllers\SeoController::class, 'save']);

        Route::get('/moderation/news', [\App\Http\Controllers\NewsController::class, 'moderationIndex']);
        Route::post('/moderation/news', [\App\Http\Controllers\NewsController::class, 'store']);
        Route::get('/moderation/news/{news:slug}', [\App\Http\Controllers\NewsController::class, 'moderationShow']);
        Route::post('/moderation/news/{news:slug}', [\App\Http\Controllers\NewsController::class, 'update']);
        Route::post('/moderation/news/{news:slug}/image', [\App\Http\Controllers\NewsController::class, 'uploadImage']);
        Route::delete('/moderation/news/{news:slug}/image', [\App\Http\Controllers\NewsController::class, 'removeImage']);
        Route::delete('/moderation/news/{news:slug}', [\App\Http\Controllers\NewsController::class, 'destroy']);

        Route::post('/moderation/news/{news:slug}/check', [\App\Http\Controllers\NewsController::class, 'sendToTextRu']);

        Route::get('/moderation/events', [\App\Http\Controllers\EventsController::class, 'moderationIndex']);
        Route::post('/moderation/events', [\App\Http\Controllers\EventsController::class, 'store']);
        Route::get('/moderation/events/{event:slug}', [\App\Http\Controllers\EventsController::class, 'moderationShow']);
        Route::post('/moderation/events/{event:slug}', [\App\Http\Controllers\EventsController::class, 'update']);
        Route::post('/moderation/events/{event:slug}/image', [\App\Http\Controllers\EventsController::class, 'uploadImage']);
        Route::delete('/moderation/events/{event:slug}/image', [\App\Http\Controllers\EventsController::class, 'removeImage']);
        Route::delete('/moderation/events/{event:slug}', [\App\Http\Controllers\EventsController::class, 'destroy']);

    });
});

Route::prefix('blog')->group(function () {
    // Публичные маршруты
    Route::get('articles', [\App\Http\Controllers\ArticleController::class, 'index']);
    Route::get('articles/{article:slug}', [\App\Http\Controllers\ArticleController::class, 'show']);
    Route::get('articles/{article:slug}/comments', [\App\Http\Controllers\ArticleController::class, 'getComments']);
    Route::get('categories', [\App\Http\Controllers\ArticleCategoryController::class, 'index']);
    Route::get('categories/{category:slug}', [\App\Http\Controllers\ArticleCategoryController::class, 'show']);

    // Просмотры
    Route::post('articles/{article}/view', [\App\Http\Controllers\ArticleController::class, 'recordView']);

    // Авторизованные маршруты
    Route::middleware('auth:sanctum')->group(function () {
        // CRUD статей
        Route::post('articles', [\App\Http\Controllers\ArticleController::class, 'store']);
        Route::put('articles/{article}', [\App\Http\Controllers\ArticleController::class, 'update']);
        Route::delete('articles/{article}', [\App\Http\Controllers\ArticleController::class, 'destroy']);

        // Реакции
        Route::post('articles/{article}/react', [\App\Http\Controllers\ArticleController::class, 'react']);
        Route::delete('articles/{article}/react', [\App\Http\Controllers\ArticleController::class, 'unreact']);

        // Комментарии
        Route::post('articles/{article}/comments', [\App\Http\Controllers\ArticleController::class, 'storeComment']);
        Route::put('comments/{comment}', [\App\Http\Controllers\ArticleController::class, 'updateComment']);
        Route::delete('comments/{comment}', [\App\Http\Controllers\ArticleController::class, 'deleteComment']);
    });

    // Админские маршруты
    Route::middleware(['auth:sanctum', 'role:admin,moderator'])->prefix('admin')->group(function () {
        Route::post('categories', [\App\Http\Controllers\ArticleCategoryController::class, 'store']);
        Route::put('categories/{category}', [\App\Http\Controllers\ArticleCategoryController::class, 'update']);
        Route::delete('categories/{category}', [\App\Http\Controllers\ArticleCategoryController::class, 'destroy']);
    });
});
