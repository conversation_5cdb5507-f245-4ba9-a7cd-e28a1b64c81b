<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\ImportPostQueue;
use App\Models\Index\IndexGunsBrokerPost;
use App\Services\Parse\GunsbrokerService;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ImportGunsbrokerController extends Controller
{
    /**
     * @throws ConnectionException
     */
    public function show(Request $request)
    {
        $validated = $request->validate([
            'link' => 'required|url|starts_with:https://gunsbroker.ru/',
        ]);

        $service = new GunsbrokerService;
        $user = Auth::user();

        $link = $validated['link'];

        // Определяем тип ссылки
        $path = parse_url($link, PHP_URL_PATH);
        $pathParts = explode('/', trim($path, '/'));

        // Проверяем, является ли это ссылкой на объявление или профиль пользователя
        // Допустимые форматы:
        // 1. /hunting/482092_sayga-12k-isp-030.html (объявление)
        $isPostLink = count($pathParts) >= 2 && preg_match('/^\d+_.*\.html$/', $pathParts[1]);

        if (! $isPostLink) {
            return response()->json([
                'errors' => [
                    'link' => ['Неподдерживаемый формат ссылки. Поддерживаются только ссылки на объявления или профили пользователей'],
                ],
            ], 422);
        }

        $postData = $service->parsePost($link, $user->id);
        $category = Category::where('slug', $postData['category'])->first();
        if (! $category) {
            return response()->json([
                'errors' => [
                    'link' => ['Данная категория еще не поддерживается'],
                ],
            ], 422);
        }

        if (env('APP_ENV') === 'local' || $user->role === 'admin') {
            return response()->json([
                'ok' => true,
                'type' => 'post',
                'data' => $postData,
            ]);
        }

        if ($user->phone !== $postData['user_phone']) {
            return response()->json([
                'errors' => [
                    'link' => ['Это не ваше объявление. Поддерживаются только объявления, опубликованные вами'],
                ],
            ], 422);
        }

        return response()->json([
            'ok' => true,
            'type' => 'post',
            'data' => $postData,
        ]);
    }

    /**
     * @throws \Throwable
     */
    public function save(Request $request) // : JsonResponse
    {
        $validated = $request->validate([
            'link' => 'required|url|starts_with:https://gunsbroker.ru/',
        ]);

        $indexGunsBrokerPost = IndexGunsBrokerPost::where('source', $validated['link'])->firstOrFail();
        $service = new GunsbrokerService;
        $post = $service->savePost($indexGunsBrokerPost, Auth::id());

        ImportPostQueue::upsert([
            'source' => $post->source,
            'source_name' => 'gunsbroker',
            'user_name' => $indexGunsBrokerPost['user_name'],
            'user_phone' => $indexGunsBrokerPost['user_phone'],
            'user_avatar' => $indexGunsBrokerPost['user_avatar'],
        ], [
            'source',
        ]);

        return response()->json([
            'ok' => (bool) $post,
            'post' => $post,
        ]);
    }
}
