<?php

namespace App\Console\Commands;

use App\Jobs\ProcessImportPostQueueJob;
use App\Models\ImportPostQueue;
use Illuminate\Console\Command;

class RetryFailedImportPostsCommand extends Command
{
    protected $signature = 'import:gunsbroker:retry {--max-attempts=3 : Максимальное количество попыток}';

    protected $description = 'Повторно запускает неудачные джобы импорта объявлений Gunsbroker';

    public function handle(): void
    {
        $maxAttempts = (int) $this->option('max-attempts');
        
        $failedPosts = ImportPostQueue::where('source_name', 'gunsbroker')
            ->whereNotNull('failed_at')
            ->where('attempts', '<', $maxAttempts)
            ->whereNotIn('status', ['completed'])
            ->get();

        if ($failedPosts->isEmpty()) {
            $this->info('Нет неудачных записей для повторной обработки');
            return;
        }

        $this->info("Найдено {$failedPosts->count()} неудачных записей для повторной обработки");

        $bar = $this->output->createProgressBar($failedPosts->count());
        $bar->start();

        foreach ($failedPosts as $post) {
            // Сбрасываем информацию об ошибке
            $post->update([
                'failed_at' => null,
                'error_message' => null
            ]);

            ProcessImportPostQueueJob::dispatch($post);
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info("Запущено {$failedPosts->count()} джоб для повторной обработки");
    }
}
