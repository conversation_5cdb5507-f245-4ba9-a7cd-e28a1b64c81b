{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "bavix/laravel-wallet": "^11.4", "beyondcode/laravel-comments": "^1.8", "cmgmyr/messenger": "^2.31", "cybercog/laravel-love": "^10.1", "drnxloc/laravel-simple-html-dom": "^1.9", "elegantly/laravel-media": "^3.4", "ichtrojan/laravel-otp": "^2.0", "itsgoingd/clockwork": "^5.3", "jeroen-g/explorer": "^4.1", "laravel-notification-channels/telegram": "^6.0", "laravel/framework": "^v11.6.1", "laravel/horizon": "^5.33", "laravel/octane": "^2.9", "laravel/reverb": "^1.5", "laravel/sanctum": "^4.0", "laravel/scout": "^10.14", "laravel/tinker": "^2.10.1", "league/flysystem-aws-s3-v3": "^3.0", "league/flysystem-read-only": "^3.0", "overtrue/laravel-favorite": "^5.3", "pdphilip/elasticsearch": "^5.0", "predis/predis": "^2.0", "propaganistas/laravel-phone": "^5.3", "pusher/pusher-php-server": "^7.2", "robokassa/sdk-php": "dev-main", "sentry/sentry-laravel": "^4.15.1", "somarkesen/telegram-gateway": "^1.2", "spatie/image": "^3.8", "spatie/laravel-sluggable": "^3.7", "spatie/laravel-tags": "^4.10", "vinkla/hashids": "^12.0", "yoomoney/yookassa-sdk-php": "^3.8"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel-lang/lang": "^15.19", "laravel/pail": "^1.2.2", "laravel/pint": "^1.21", "laravel/sail": "^1.41", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "phpunit/phpunit": "^11.5.3"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}