<?php

namespace App\Console\Commands;

use App\Events\PostChangeEvent;
use App\Models\ImportPostQueue;
use App\Models\Index\IndexGunsBrokerPost;
use App\Models\Post;
use App\Models\RefModeration;
use App\Models\User;
use App\Services\Parse\GunsbrokerService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Http\Client\ConnectionException;
use Symfony\Component\Console\Helper\ProgressBar;

class ProcessImportPostsQueueCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:gunsbroker:queue';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    public function __construct(
        private GunsbrokerService $service
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @throws ConnectionException
     */
    public function handle()
    {
        // Единый прогресс-бар для всего процесса
        $posts = ImportPostQueue::where('source_name', 'gunsbroker')
            ->get();

        // +3 шага под этапы: draft, process, photos
        $countTotal = $posts->count();
        $bar = $this->output->createProgressBar($countTotal + 3);
        $bar->setFormat('%current%/%max% [%bar%] %percent:3s%% | %message%');
        $bar->setMessage('Старт');
        $bar->start();

        // Этап: поиск объявлений с percentage < 100% (и обработка ватермарки если требуется)
        $bar->setMessage('Этап: поиск неполностью обработанных объявлений');
        $this->findIncompletePhotos($posts, $bar);

        // Этап: обработка входящих (draft)
        $bar->setMessage('Этап: обработка входящих (draft)');
        $this->processDraft();
        $bar->advance();

        // Этап: парсинг объявлений (process)
        $bar->setMessage('Этап: парсинг объявлений (process)');
        $this->processProcess();
        $bar->advance();

        // Этап: удаление ватермарков (photos)
        $bar->setMessage('Этап: удаление ватермарков (photos)');
        $this->processPhotos();
        $bar->advance();

        // Завершение
        $bar->setMessage('Готово');
        $bar->finish();
        $this->newLine();

    }

    /**
     * Находит элементы из очереди gunsbroker со статусом 'photos',
     * у которых процент удаления ватермарки (по URL фото) < 100%.
     * Ничего не обрабатывает — только выводит список.
     */
    private function findIncompletePhotos($posts = null, ?ProgressBar $bar = null): void
    {
        if ($posts === null) {
            $posts = ImportPostQueue::where('source_name', 'gunsbroker')
                ->get();
        }

        $countTotal = $posts->count();
        $countIncomplete = 0;
        $countParsed = 0;
        $countErrors = 0;

        $externalBar = true;
        if (! $bar) {
            $externalBar = false;
            $bar = $this->output->createProgressBar($countTotal);
            $bar->setFormat('%current%/%max% [%bar%] %percent:3s%% | %message%');
            $bar->setMessage('Старт');
            $bar->start();
        }

        $i = 0;
        foreach ($posts as $queuePost) {
            $i++;
            $bar->setMessage("[$i/$countTotal] Проверяем {$queuePost->source}");

            $product = IndexGunsBrokerPost::where('source', $queuePost->source)->first();
            if (! $product) {
                // Если источник не найден в индексе — пробуем спарсить его
                try {
                    $user = User::where('phone', $queuePost->user_phone)->first();
                    $bar->setMessage("[$i/$countTotal] PARSE {$queuePost->source}");
                    $this->service->parsePost($queuePost->source, $user?->id);
                    $countParsed++;
                } catch (\Throwable $e) {
                    $countErrors++;
                    $bar->setMessage("[$i/$countTotal] PARSE ERROR {$queuePost->source}: ".$e->getMessage());
                    $bar->advance();

                    continue;
                }

                // Повторная попытка найти продукт после парсинга
                $product = IndexGunsBrokerPost::where('source', $queuePost->source)->first();
                if (! $product) {
                    $bar->setMessage("[$i/$countTotal] NOT FOUND AFTER PARSE {$queuePost->source}");
                    $bar->advance();

                    continue;
                }
            }

            $photos = $product->photos ?? [];
            $total = count($photos);
            $match = 0;

            if ($total > 0) {
                foreach ($photos as $photo) {
                    if (! str_contains($photo['url'] ?? '', 'gunsbroker.ru')) {
                        $match++;
                    }
                }
            }

            $percentage = $total ? round($match / $total * 100, 2) : 0;

            if ($percentage >= 0 && $percentage < 100) {
                $countIncomplete++;
                $bar->setMessage("[$i/$countTotal] INCOMPLETE {$queuePost->source} — {$percentage}% ({$match}/{$total}) | inc={$countIncomplete} parsed={$countParsed} err={$countErrors}");
            } else {
                $bar->setMessage("[$i/$countTotal] OK {$queuePost->source} — {$percentage}% ({$match}/{$total}) | inc={$countIncomplete} parsed={$countParsed} err={$countErrors}");
            }

            $this->service->processRemoveWatermark($product);

            $bar->advance();
        }

        $bar->setMessage("ГОТОВО: inc={$countIncomplete} из {$countTotal}, parsed={$countParsed}, err={$countErrors}");
        if (! $externalBar) {
            $bar->finish();
            $this->newLine();
        }
    }

    private function setPhotosProcess(ImportPostQueue $queuePost)
    {
        // Пересчитываем продукт и фото после обработки
        $product = IndexGunsBrokerPost::where('source', $queuePost->source)->firstOrFail();
        $photos = $product->photos ?? [];
        $total = count($photos);
        $processedCount = 0;
        $newProcess = $queuePost->process;

        foreach ($photos as $photo) {
            // Считаем «обработанные» фото (URL без 'gunsbroker.ru')
            if (! str_contains($photo['url'], 'gunsbroker.ru')) {
                $processedCount++;
            }
        }

        if ($total > 0) {
            // Переносим прогресс из диапазона 0–100 в 20–100:
            // при 0 фото — process = 20, при all обработанных — process = 100
            $increment = (int) ceil($processedCount * 80 / $total);
            $newProcess = min(100, 20 + $increment);

            // Сохраняем прогресс в очередь
            $queuePost->process = $newProcess;
            $queuePost->save();
        }

        $newProcess = intval($newProcess);

        if ($newProcess === 100 or $total === 0) {
            $queuePost->status = 'completed';
            $queuePost->save();

            $post = Post::withoutGlobalScopes()->where('source', $queuePost->source)->firstOrFail();
            $post->moderation_id = RefModeration::IS_APPROVED;
            $post->published_at = Carbon::now();
            $post->save();
            PostChangeEvent::dispatch($post);

            $this->info('Completed: '.$post->category->slug.'/'.$post->slug);
        } else {
            $this->line('Process: '.$newProcess.'% - '.$product->source);
        }
    }

    private function processPhotos()
    {
        $posts = ImportPostQueue::where('source_name', 'gunsbroker')
            ->where('status', 'photos')
            ->get();

        $this->withProgressBar($posts, function (ImportPostQueue $queuePost) {
            try {
                $this->line("Получаем фото для: $queuePost->source");

                $product = IndexGunsBrokerPost::where('source', $queuePost->source)->first();
                if (! $product) {
                    $queuePost->delete();
                    $this->warn('Не найдет source объявление, удаляем ImportPostQueue');

                    return;
                }

                $this->setPhotosProcess($queuePost);

                $service = new GunsbrokerService;
                $service->processRemoveWatermark($product);

                $this->setPhotosProcess($queuePost);
            } catch (\Throwable $throwable) {
                $this->error($queuePost->source.' - '.$throwable->getMessage());
            }
        });
    }

    private function processProcess()
    {
        $posts = ImportPostQueue::where('source_name', 'gunsbroker')
            ->where('status', 'process')
            ->get();

        $this->withProgressBar($posts, function (ImportPostQueue $queuePost) {

            try {
                $post = Post::withoutGlobalScopes()->where('source', $queuePost->source)->first();
                $user = User::where('phone', $queuePost->user_phone)->firstOrFail();

                if ($post) {
                    $this->warn('Exists: '.$queuePost->source);
                } else {
                    $product = IndexGunsBrokerPost::where('source', $queuePost->source)->firstOrFail();

                    $post = $this->service->savePost($product, $user->id);
                    $this->info('Processed: '.$post->source);
                }

                $queuePost->status = 'photos';
                $queuePost->process = 20;
                $queuePost->save();

            } catch (\Throwable $exception) {
                $this->warn('Error: '.$queuePost->source);
                $this->error($exception->getMessage());
                $queuePost->delete();
            }
        });
    }

    /**
     * @throws ConnectionException
     */
    private function processDraft(): void
    {
        $queuePosts = ImportPostQueue::where('source_name', 'gunsbroker')
            ->where('status', 'draft')
            ->get();

        $this->withProgressBar($queuePosts, function (ImportPostQueue $queuePost) {
            $user = User::where('phone', $queuePost->user_phone)->first();

            if (! $user) {
                $user = User::create([
                    'name' => $queuePost->user_name,
                    'phone' => $queuePost->user_phone,
                ]);
            }

            try {
                $this->service->parsePost($queuePost->source, $user->id);
                $queuePost->status = 'process';
                $queuePost->process = 10;
                $queuePost->save();
            } catch (\Throwable $throwable) {
                $this->error($throwable->getMessage());
            }
        });
    }
}
