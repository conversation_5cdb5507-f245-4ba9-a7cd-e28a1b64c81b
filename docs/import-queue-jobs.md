# Система джоб для обработки очереди импорта Gunsbroker

## Обзор

Команда `ProcessImportPostsQueueCommand` была переделана в систему джоб для более надежной обработки импорта объявлений с поддержкой ретраев и мониторинга ошибок.

## Компоненты системы

### 1. Джоба ProcessImportPostQueueJob

Основная джоба для обработки одной записи ImportPostQueue.

**Особенности:**
- 3 попытки выполнения (`$tries = 3`)
- Тайм<PERSON>ут 5 минут (`$timeout = 300`)
- Задержка между попытками 1 минута (`$backoff = 60`)
- Очередь `import` (`$onQueue('import')`)

**Этапы обработки:**
1. `draft` - парсинг объявления и создание пользователя
2. `process` - создание объявления в базе данных
3. `photos` - обработка фотографий и удаление ватермарков
4. `completed` - завершение обработки

### 2. События и слушатели

- `ImportPostQueueCreated` - событие создания записи в очереди
- `ProcessImportPostQueueListener` - автоматически запускает джобу при создании записи

### 3. Модель ImportPostQueue

Добавлены новые поля:
- `failed_at` - время последней неудачной попытки
- `error_message` - сообщение об ошибке
- `attempts` - количество попыток обработки

## Команды

### Универсальная команда управления

```bash
# Обработать все незавершенные записи
php artisan import:gunsbroker:manage process

# Обработать только записи с определенным статусом
php artisan import:gunsbroker:manage process --status=draft
php artisan import:gunsbroker:manage process --status=process
php artisan import:gunsbroker:manage process --status=photos

# Повторить неудачные джобы (максимум 3 попытки)
php artisan import:gunsbroker:manage retry

# Повторить с другим лимитом попыток
php artisan import:gunsbroker:manage retry --max-attempts=5

# Удалить завершенные записи старше 7 дней
php artisan import:gunsbroker:manage cleanup

# Удалить завершенные записи старше 30 дней
php artisan import:gunsbroker:manage cleanup --days=30
```

### Старая команда (для справки)

```bash
# Старая синхронная команда (не рекомендуется)
php artisan import:gunsbroker:queue
```

## Автоматический запуск

Джобы запускаются автоматически при:

1. **Создании записи через ImportController** - при импорте профиля пользователя
2. **Создании записи через ImportGunsbrokerController** - при сохранении отдельного объявления

## Мониторинг

### Логи

Все операции логируются в стандартные логи Laravel:
- Начало обработки
- Ошибки с полным стеком
- Завершение этапов

### Статусы записей

- `draft` - ожидает парсинга
- `process` - ожидает создания объявления
- `photos` - ожидает обработки фотографий
- `completed` - обработка завершена

### Отслеживание ошибок

Проверить неудачные записи:

```sql
SELECT id, source, status, attempts, failed_at, error_message 
FROM import_post_queues 
WHERE failed_at IS NOT NULL 
AND status != 'completed';
```

## Настройка очередей

Убедитесь, что настроена очередь `import`:

```bash
# Запуск воркера для очереди import
php artisan queue:work --queue=import

# Или для всех очередей
php artisan queue:work
```

## Преимущества новой системы

1. **Надежность** - автоматические ретраи при ошибках
2. **Масштабируемость** - можно запускать несколько воркеров
3. **Мониторинг** - отслеживание ошибок и попыток
4. **Гибкость** - можно обрабатывать записи по отдельности
5. **Автоматизация** - джобы запускаются автоматически при создании записей

## Миграция со старой системы

1. Запустите миграцию для добавления новых полей:
   ```bash
   php artisan migrate
   ```

2. Для существующих записей в очереди запустите:
   ```bash
   php artisan import:gunsbroker:manage process
   ```

3. Настройте воркеры очередей в production
