<?php

namespace App\Console\Commands;

use App\Jobs\ProcessImportPostQueueJob;
use App\Models\ImportPostQueue;
use Illuminate\Console\Command;

class ProcessImportPostsQueueJobCommand extends Command
{
    protected $signature = 'import:gunsbroker:queue:jobs {--status= : Обработать только записи с определенным статусом}';

    protected $description = 'Запускает джобы для обработки очереди импорта объявлений Gunsbroker';

    public function handle(): void
    {
        $query = ImportPostQueue::where('source_name', 'gunsbroker');
        
        if ($status = $this->option('status')) {
            $query->where('status', $status);
        } else {
            // По умолчанию обрабатываем все кроме завершенных
            $query->whereNotIn('status', ['completed']);
        }

        $posts = $query->get();
        
        if ($posts->isEmpty()) {
            $this->info('Нет записей для обработки');
            return;
        }

        $this->info("Найдено {$posts->count()} записей для обработки");

        $bar = $this->output->createProgressBar($posts->count());
        $bar->start();

        foreach ($posts as $post) {
            ProcessImportPostQueueJob::dispatch($post);
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info("Запущено {$posts->count()} джоб для обработки");
    }
}
