x-logging: &default-logging
    driver: "json-file"
    options:
        max-size: "50m"
        max-file: "6"

#x-base: &base
#    build:
#        context: .
#        dockerfile: infrastructure/Dockerfile
#    volumes:
#        - '.:/app'
#    networks:
#        - net
#    restart: always
#    logging: *default-logging

services:
    php:
        build:
            context: .
            dockerfile: infrastructure/Dockerfile
        entrypoint: php artisan octane:frankenphp --workers=1 --max-requests=1
        volumes:
            - '.:/app:rw'
        networks:
            - net
        ports:
            - '80:8000'

#    task:
#        <<: *base
#        entrypoint: php artisan schedule:work
#
#    queue:
#        <<: *base
#        entrypoint: php artisan queue:work --tries=3


    reverb:
        build:
            context: .
            dockerfile: infrastructure/Dockerfile
        volumes:
            - '.:/app'
        networks:
            - net
        entrypoint: php artisan reverb:start --debug
        logging: *default-logging

    pgsql:
        image: 'postgres:17'
        ports:
            - '${FORWARD_DB_PORT:-5432}:5432'
        environment:
            PGPASSWORD: '${DB_PASSWORD:-secret}'
            POSTGRES_DB: '${DB_DATABASE}'
            POSTGRES_USER: '${DB_USERNAME}'
            POSTGRES_PASSWORD: '${DB_PASSWORD:-secret}'
        volumes:
            - 'pgsql:/var/lib/postgresql/data'
        networks:
            - net
        healthcheck:
            test: [ "CMD", "pg_isready", "-U", "${DB_USERNAME}", "-d", "${DB_DATABASE}" ]
            timeout: 5s
            retries: 3
        logging: *default-logging

    redis:
        image: 'redis:alpine'
        command: [ "redis-server", "--maxmemory", "2gb", "--maxmemory-policy", "allkeys-lru" ]
        ports:
            - '${FORWARD_REDIS_PORT:-6379}:6379'
        volumes:
            - 'redis:/data'
        networks:
            - net
        healthcheck:
            test: [ "CMD", "redis-cli", "ping" ]
            timeout: 5s
            retries: 3
        logging:
            driver: "none"

    elasticsearch:
        image: 'docker.elastic.co/elasticsearch/elasticsearch:8.10.2'
        environment:
            - discovery.type=single-node
            - xpack.security.enabled=false
            - ES_JAVA_OPTS=-Xms1g -Xmx1g
        ports:
            - '9200:9200'
        volumes:
            - 'elasticsearch:/usr/share/elasticsearch/data'
        networks:
            - net
        restart: on-failure
        healthcheck:
            test: [ "CMD", "curl", "-f", "http://localhost:9200" ]
            timeout: 5s
            retries: 3
        logging:
            driver: "none"

    tor-proxy:
        image: "dperson/torproxy"
        environment:
            - TOR_MaxCircuitDirtiness=1
            - TOR_NewCircuitPeriod=1
            - TOR_EnforceDistinctSubnets=1
        networks:
            - net
        restart: always
        healthcheck:
            test: [ "CMD", "curl", "-f", "--socks5-hostname", "localhost:9050", "http://httpbin.org/ip" ]
            interval: 30s
            timeout: 10s
            retries: 3
            start_period: 10s
        logging: *default-logging
    #    kibana:
    #        image: kibana:8.10.2
    #        ports:
    #            - '5601:5601'
    #        networks:
    #            - net
    #        depends_on:
    #            - elasticsearch
    #        environment:
    #            ELASTICSEARCH_HOSTS: ${ES_HOSTS}
    #            ELASTIC_USERNAME: ${ES_USERNAME}
    #            ELASTIC_PASSWORD: ${ES_PASSWORD}
    watermark-api:
        build:
            context: erase-watermark
            dockerfile: Dockerfile
        ports:
            - "3001:3001"
        networks:
            - net
        depends_on:
            - tor-proxy
        restart: always
#        healthcheck:
#            test: [ "CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3001/health" ]
#            interval: 30s
#            timeout: 10s
#            retries: 3
#            start_period: 10s
        logging: *default-logging

networks:
    net:
        driver: bridge
volumes:
    pgsql:
    redis:
    elasticsearch:
