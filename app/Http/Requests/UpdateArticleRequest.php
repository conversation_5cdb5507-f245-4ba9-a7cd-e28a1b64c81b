<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateArticleRequest extends FormRequest
{
    public function authorize(): bool
    {
        // Проверяем, что пользователь - автор статьи
        return $this->user()->id === $this->route('article')->user_id;
    }

    public function rules(): array
    {
        return [
            'category_id' => 'exists:article_categories,id',
            'title' => 'string|min:5|max:255',
            'excerpt' => 'nullable|string|max:500',
            'content' => 'json',
            'cover_image' => 'nullable|string|max:500',
            'status' => Rule::in(['draft', 'moderation']),
            'allow_comments' => 'boolean',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:50'
        ];
    }
}
