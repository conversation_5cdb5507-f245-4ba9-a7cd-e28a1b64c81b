<?php

namespace App\Console\Commands;

use App\Models\ImportPostQueue;
use Illuminate\Console\Command;

class CleanupImportPostsQueueCommand extends Command
{
    protected $signature = 'import:gunsbroker:cleanup {--days=7 : Количество дней для хранения завершенных записей}';

    protected $description = 'Очищает старые завершенные записи из очереди импорта';

    public function handle(): void
    {
        $days = (int) $this->option('days');
        
        $cutoffDate = now()->subDays($days);
        
        $deletedCount = ImportPostQueue::where('source_name', 'gunsbroker')
            ->where('status', 'completed')
            ->where('updated_at', '<', $cutoffDate)
            ->delete();

        $this->info("Удалено {$deletedCount} завершенных записей старше {$days} дней");
    }
}
